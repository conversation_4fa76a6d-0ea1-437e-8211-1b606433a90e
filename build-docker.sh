#!/bin/bash

# Build script for OpenHands with GitHub Enterprise support
# This script builds the Docker image with proper error handling

set -e  # Exit on any error

echo "Building OpenHands Docker image with GitHub Enterprise support..."

# Check if EMC_CA_ROOT.crt exists
if [ ! -f "EMC_CA_ROOT.crt" ]; then
    echo "ERROR: EMC_CA_ROOT.crt file not found!"
    echo "Please ensure the certificate file is in the current directory."
    exit 1
fi

echo "Found EMC_CA_ROOT.crt certificate file"

# Set environment variables for the build
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "Using GitHub Enterprise domain: $GITHUB_BASE_DOMAIN"

# Build the Docker image
echo "Starting Docker build..."
docker-compose build --no-cache

if [ $? -eq 0 ]; then
    echo "✅ Docker build completed successfully!"
    echo ""
    echo "To run OpenHands with GitHub Enterprise support:"
    echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up"
    echo ""
    echo "Or set the environment variable permanently:"
    echo "  export GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN"
    echo "  docker-compose up"
else
    echo "❌ Docker build failed!"
    echo ""
    echo "Common troubleshooting steps:"
    echo "1. Ensure EMC_CA_ROOT.crt is in the correct format"
    echo "2. Check Docker daemon is running"
    echo "3. Try building with --no-cache flag"
    echo "4. Check available disk space"
    exit 1
fi
