#!/bin/bash

# Build script for OpenHands with GitHub Enterprise support
# This script builds the Docker image with proper error handling

set -e  # Exit on any error

echo "Building OpenHands Docker image with GitHub Enterprise support..."

# Check if EMC_CA_ROOT.crt exists
if [ ! -f "EMC_CA_ROOT.crt" ]; then
    echo "ERROR: EMC_CA_ROOT.crt file not found!"
    echo "Please ensure the certificate file is in the current directory."
    exit 1
fi

echo "Found EMC_CA_ROOT.crt certificate file"

# Set environment variables for the build
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "Using GitHub Enterprise domain: $GITHUB_BASE_DOMAIN"

# Check if we should use corporate-friendly Dockerfile
if [ "$USE_CORPORATE_DOCKERFILE" = "true" ]; then
    echo "Using corporate-friendly Dockerfile..."
    cp containers/app/Dockerfile.corporate containers/app/Dockerfile.backup
    mv containers/app/Dockerfile containers/app/Dockerfile.original
    mv containers/app/Dockerfile.corporate containers/app/Dockerfile
fi

# Build the Docker image
echo "Starting Docker build..."
if docker-compose build --no-cache; then
    echo "Build successful with standard approach"
else
    echo "Standard build failed, trying corporate-friendly approach..."

    # Restore original and try corporate version
    if [ -f "containers/app/Dockerfile.original" ]; then
        mv containers/app/Dockerfile containers/app/Dockerfile.failed
        mv containers/app/Dockerfile.original containers/app/Dockerfile
    fi

    if [ -f "containers/app/Dockerfile.backup" ]; then
        mv containers/app/Dockerfile containers/app/Dockerfile.original
        mv containers/app/Dockerfile.backup containers/app/Dockerfile
        echo "Retrying with corporate-friendly settings..."
        docker-compose build --no-cache
    else
        echo "Corporate Dockerfile not available, build failed"
        exit 1
    fi
fi

if [ $? -eq 0 ]; then
    echo "✅ Docker build completed successfully!"
    echo ""
    echo "To run OpenHands with GitHub Enterprise support:"
    echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up"
    echo ""
    echo "Or set the environment variable permanently:"
    echo "  export GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN"
    echo "  docker-compose up"
else
    echo "❌ Docker build failed!"
    echo ""
    echo "Common troubleshooting steps:"
    echo "1. Ensure EMC_CA_ROOT.crt is in the correct format"
    echo "2. Check Docker daemon is running"
    echo "3. Try building with --no-cache flag"
    echo "4. Check available disk space"
    exit 1
fi
