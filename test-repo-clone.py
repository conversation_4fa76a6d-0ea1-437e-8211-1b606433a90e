#!/usr/bin/env python3
"""
Test script to verify GitHub Enterprise repository cloning configuration
"""

import os
import sys

def test_github_enterprise_repo_config():
    """Test GitHub Enterprise repository configuration"""
    
    print("🔍 Testing GitHub Enterprise Repository Configuration")
    print("=" * 60)
    
    # Test environment variable
    github_domain = os.environ.get('GITHUB_BASE_DOMAIN', 'github.com')
    print(f"📋 GITHUB_BASE_DOMAIN: {github_domain}")
    
    # Test repository URL construction
    repo_name = "wux13/POC6"
    
    print(f"\n🔗 Repository URL Construction for: {repo_name}")
    print("-" * 40)
    
    # Standard GitHub
    standard_url = f"https://github.com/{repo_name}.git"
    print(f"Standard GitHub: {standard_url}")
    
    # GitHub Enterprise
    enterprise_url = f"https://{github_domain}/{repo_name}.git"
    print(f"GitHub Enterprise: {enterprise_url}")
    
    # With authentication
    token_placeholder = "YOUR_TOKEN_HERE"
    auth_enterprise_url = f"https://{token_placeholder}@{github_domain}/{repo_name}.git"
    print(f"With Auth: {auth_enterprise_url}")
    
    print(f"\n🎯 Expected Clone Command:")
    print(f"git clone {enterprise_url} POC6")
    
    # Test configuration loading
    print(f"\n⚙️ Testing Configuration Loading:")
    try:
        # Add current directory to Python path
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from openhands.core.config.utils import load_app_config
        app_config = load_app_config()
        config_domain = getattr(app_config, 'github_base_domain', 'github.com')
        print(f"✅ Config github_base_domain: {config_domain}")
        
        # Test the logic used in the runtime
        final_domain = os.environ.get('GITHUB_BASE_DOMAIN', config_domain)
        print(f"✅ Final domain (env override): {final_domain}")
        
    except Exception as e:
        print(f"❌ Error loading config: {e}")
    
    print(f"\n🔧 Troubleshooting Steps:")
    print("1. Verify the repository exists on your GitHub Enterprise:")
    print(f"   https://{github_domain}/wux13/POC6")
    print("2. Check if you have access to the repository")
    print("3. Ensure your GitHub token has the necessary permissions")
    print("4. Test manual clone:")
    print(f"   git clone https://{github_domain}/wux13/POC6.git")

if __name__ == "__main__":
    test_github_enterprise_repo_config()
