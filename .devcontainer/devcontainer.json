// For format details, see: https://aka.ms/devcontainer.json
{
	"name": "Python 3",
	// Documentation for this image:
	// - https://github.com/devcontainers/templates/tree/main/src/python
	// - https://github.com/microsoft/vscode-remote-try-python
	// - https://hub.docker.com/r/microsoft/devcontainers-python
	"image": "mcr.microsoft.com/devcontainers/python:1-3.12-bullseye",
	"features": {
		"ghcr.io/devcontainers/features/docker-outside-of-docker:1": {},
		"ghcr.io/devcontainers-extra/features/poetry:2": {},
		"ghcr.io/devcontainers/features/node:1": {},
	},
	"postCreateCommand": ".devcontainer/setup.sh",
}
