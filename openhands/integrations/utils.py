import os

from pydantic import SecretStr

from openhands.core.config.utils import load_app_config
from openhands.integrations.github.github_service import GitHubService
from openhands.integrations.gitlab.gitlab_service import GitLabService
from openhands.integrations.provider import ProviderType


async def validate_provider_token(
    token: SecretStr, base_domain: str | None = None
) -> ProviderType | None:
    """
    Determine whether a token is for GitHub or GitLab by attempting to get user info
    from both services.

    Args:
        token: The token to check

    Returns:
        'github' if it's a GitHub token
        'gitlab' if it's a GitLab token
        None if the token is invalid for both services
    """
    # Get default base domain from config if not provided
    if base_domain is None:
        try:
            app_config = load_app_config()
            base_domain = os.environ.get('GITHUB_BASE_DOMAIN', app_config.github_base_domain)
        except Exception:
            base_domain = os.environ.get('GITHUB_BASE_DOMAIN', 'github.com')

    # Try GitHub first
    try:
        github_service = GitHubService(token=token, base_domain=base_domain)
        await github_service.verify_access()
        return ProviderType.GITHUB
    except Exception:
        pass

    # Try GitLab next
    try:
        gitlab_service = GitLabService(token=token, base_domain=base_domain)
        await gitlab_service.get_user()
        return ProviderType.GITLAB
    except Exception:
        pass

    return None
