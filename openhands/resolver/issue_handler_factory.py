import os

from openhands.core.config import LLMConfig
from openhands.core.config.utils import load_app_config
from openhands.integrations.provider import ProviderType
from openhands.resolver.interfaces.github import G<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GithubPR<PERSON>and<PERSON>
from openhands.resolver.interfaces.gitlab import Gitla<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, GitlabPRHand<PERSON>
from openhands.resolver.interfaces.issue_definitions import (
    ServiceContextIssue,
    ServiceContextPR,
)


class IssueHandlerFactory:
    def __init__(
        self,
        owner: str,
        repo: str,
        token: str,
        username: str,
        platform: ProviderType,
        base_domain: str | None,
        issue_type: str,
        llm_config: LLMConfig,
    ) -> None:
        self.owner = owner
        self.repo = repo
        self.token = token
        self.username = username
        self.platform = platform

        # Get default base domain from config if not provided
        if base_domain is None and platform == ProviderType.GITHUB:
            try:
                app_config = load_app_config()
                base_domain = os.environ.get('GITHUB_BASE_DOMAIN', app_config.github_base_domain)
            except Exception:
                base_domain = os.environ.get('GITHUB_BASE_DOMAIN', 'github.com')
        elif base_domain is None:
            base_domain = 'gitlab.com'

        self.base_domain = base_domain
        self.issue_type = issue_type
        self.llm_config = llm_config

    def create(self) -> ServiceContextIssue | ServiceContextPR:
        if self.issue_type == 'issue':
            if self.platform == ProviderType.GITHUB:
                return ServiceContextIssue(
                    GithubIssueHandler(
                        self.owner,
                        self.repo,
                        self.token,
                        self.username,
                        self.base_domain,
                    ),
                    self.llm_config,
                )
            else:  # platform == Platform.GITLAB
                return ServiceContextIssue(
                    GitlabIssueHandler(
                        self.owner,
                        self.repo,
                        self.token,
                        self.username,
                        self.base_domain,
                    ),
                    self.llm_config,
                )
        elif self.issue_type == 'pr':
            if self.platform == ProviderType.GITHUB:
                return ServiceContextPR(
                    GithubPRHandler(
                        self.owner,
                        self.repo,
                        self.token,
                        self.username,
                        self.base_domain,
                    ),
                    self.llm_config,
                )
            else:  # platform == Platform.GITLAB
                return ServiceContextPR(
                    GitlabPRHandler(
                        self.owner,
                        self.repo,
                        self.token,
                        self.username,
                        self.base_domain,
                    ),
                    self.llm_config,
                )
        else:
            raise ValueError(f'Invalid issue type: {self.issue_type}')
