#!/bin/bash

# Quick test for clone issues
echo "🚀 Quick Clone Test"
echo "=================="

GITHUB_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
REPO_NAME="wux13/POC6"

# Test 1: Check if we can reach GitHub Enterprise
echo "1. Testing connectivity to $GITHUB_DOMAIN..."
if curl -I -m 10 https://$GITHUB_DOMAIN >/dev/null 2>&1; then
    echo "   ✅ Can connect to GitHub Enterprise"
else
    echo "   ❌ Cannot connect to GitHub Enterprise"
    echo "   Trying with insecure SSL..."
    if curl -k -I -m 10 https://$GITHUB_DOMAIN >/dev/null 2>&1; then
        echo "   ⚠️  Connection works with insecure SSL (certificate issue)"
    else
        echo "   ❌ Cannot connect even with insecure SSL (network issue)"
    fi
fi

# Test 2: Check repository accessibility
echo ""
echo "2. Testing repository accessibility..."
REPO_URL="https://$GITHUB_DOMAIN/$REPO_NAME"
if curl -I -m 10 $REPO_URL >/dev/null 2>&1; then
    echo "   ✅ Repository is accessible: $REPO_URL"
else
    echo "   ❌ Repository is not accessible: $REPO_URL"
    echo "   This could mean:"
    echo "   - Repository doesn't exist"
    echo "   - No access permissions"
    echo "   - Authentication required"
fi

# Test 3: Try manual git clone
echo ""
echo "3. Testing git clone..."
TEST_DIR="/tmp/quick_test_$$"
CLONE_URL="https://$GITHUB_DOMAIN/$REPO_NAME.git"

echo "   Trying: git clone $CLONE_URL"
if git clone $CLONE_URL $TEST_DIR >/dev/null 2>&1; then
    echo "   ✅ Git clone successful!"
    rm -rf $TEST_DIR
else
    echo "   ❌ Git clone failed"
    echo "   Error details:"
    git clone $CLONE_URL $TEST_DIR 2>&1 | head -3
    
    # Try with SSL verification disabled
    echo ""
    echo "   Trying with SSL verification disabled..."
    if GIT_SSL_NO_VERIFY=true git clone $CLONE_URL ${TEST_DIR}_nossl >/dev/null 2>&1; then
        echo "   ✅ Clone works with SSL verification disabled"
        echo "   This indicates an SSL certificate issue"
        rm -rf ${TEST_DIR}_nossl
    else
        echo "   ❌ Clone fails even with SSL verification disabled"
        echo "   Error details:"
        GIT_SSL_NO_VERIFY=true git clone $CLONE_URL ${TEST_DIR}_nossl 2>&1 | head -3
    fi
fi

# Test 4: Check containers
echo ""
echo "4. Checking OpenHands containers..."
if docker-compose ps | grep -q "Up"; then
    echo "   ✅ Containers are running"
    
    # Check app container logs for clone attempts
    APP_CONTAINER=$(docker ps -q --filter "name=openhands-app")
    if [ ! -z "$APP_CONTAINER" ]; then
        echo "   Recent clone-related logs from app container:"
        docker logs $APP_CONTAINER 2>&1 | grep -i -E "(clone|git|repo)" | tail -5 | sed 's/^/     /'
    fi
else
    echo "   ❌ Containers are not running properly"
    echo "   Container status:"
    docker-compose ps | sed 's/^/     /'
fi

echo ""
echo "🔧 Quick Fix Suggestions:"
echo ""

# Provide specific suggestions based on common issues
echo "If connectivity test failed:"
echo "  - Check VPN connection"
echo "  - Verify network access to $GITHUB_DOMAIN"
echo "  - Check corporate firewall settings"
echo ""

echo "If repository is not accessible:"
echo "  - Verify repository exists: https://$GITHUB_DOMAIN/$REPO_NAME"
echo "  - Check if you have access to the repository"
echo "  - Ensure your GitHub token has proper permissions"
echo ""

echo "If git clone failed with SSL errors:"
echo "  - Rebuild with corporate certificates: ./build-corporate.sh"
echo "  - Check EMC_CA_ROOT.crt file exists and is valid"
echo "  - Verify certificate installation in Docker image"
echo ""

echo "If git clone failed with authentication errors:"
echo "  - Set GitHub token: export GITHUB_TOKEN=your_token"
echo "  - Check token permissions for the repository"
echo "  - Verify token hasn't expired"
echo ""

echo "To restart with proper configuration:"
echo "  export GITHUB_BASE_DOMAIN=$GITHUB_DOMAIN"
echo "  docker-compose down && docker-compose up"
