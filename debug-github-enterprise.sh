#!/bin/bash

# Debug script for GitHub Enterprise configuration
echo "🔍 Debugging GitHub Enterprise Configuration"
echo "=============================================="

# Check environment variables
echo "📋 Environment Variables:"
echo "GITHUB_BASE_DOMAIN: ${GITHUB_BASE_DOMAIN:-'NOT SET'}"

# Check if containers are running
echo ""
echo "🐳 Docker Containers:"
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep openhands

# Check container logs for GitHub-related messages
echo ""
echo "📝 Recent App Container Logs (GitHub related):"
docker logs $(docker ps -q --filter "name=openhands-app") 2>&1 | grep -i "github\|clone\|repo" | tail -10

echo ""
echo "📝 Recent Runtime Container Logs (workspace related):"
docker logs $(docker ps -q --filter "name=openhands-runtime") 2>&1 | grep -i "workspace\|directory\|clone" | tail -10

# Check if workspace directory exists in runtime container
echo ""
echo "📁 Workspace Directory in Runtime Container:"
RUNTIME_CONTAINER=$(docker ps -q --filter "name=openhands-runtime")
if [ ! -z "$RUNTIME_CONTAINER" ]; then
    echo "Runtime container ID: $RUNTIME_CONTAINER"
    docker exec $RUNTIME_CONTAINER ls -la /workspace/ 2>/dev/null || echo "❌ /workspace directory not found or not accessible"
    docker exec $RUNTIME_CONTAINER ls -la /workspace/POC6/ 2>/dev/null || echo "❌ /workspace/POC6 directory not found"
else
    echo "❌ No runtime container found"
fi

# Test GitHub Enterprise connectivity from app container
echo ""
echo "🌐 Testing GitHub Enterprise Connectivity:"
APP_CONTAINER=$(docker ps -q --filter "name=openhands-app")
if [ ! -z "$APP_CONTAINER" ]; then
    echo "Testing connectivity to eos2git.cec.lab.emc.com..."
    docker exec $APP_CONTAINER curl -I https://eos2git.cec.lab.emc.com 2>/dev/null | head -1 || echo "❌ Cannot connect to GitHub Enterprise"
    
    echo "Testing API endpoint..."
    docker exec $APP_CONTAINER curl -I https://eos2git.cec.lab.emc.com/api/v3 2>/dev/null | head -1 || echo "❌ Cannot connect to GitHub Enterprise API"
else
    echo "❌ No app container found"
fi

echo ""
echo "🔧 Troubleshooting Suggestions:"
echo "1. Ensure GITHUB_BASE_DOMAIN is set: export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com"
echo "2. Check if GitHub Enterprise is accessible from your network"
echo "3. Verify GitHub token has access to the wux13/POC6 repository"
echo "4. Check if the repository exists on your GitHub Enterprise instance"
echo "5. Restart containers with: docker-compose down && docker-compose up"
