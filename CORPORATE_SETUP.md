# Corporate Environment Setup Guide

## Overview

This guide helps you set up OpenHands in corporate environments that have:
- SSL/TLS inspection (man-in-the-middle proxies)
- Package repository signature verification issues
- Custom CA certificates
- GitHub Enterprise instances

## The Problem You Encountered

The error you saw:
```
At least one invalid signature was encountered.
E: The repository 'http://deb.debian.org/debian bookworm InRelease' is not signed.
```

This happens because corporate SSL inspection modifies the signatures of package repositories, causing apt to reject them as "unsigned."

## Solution

### Quick Fix (Recommended)

Use the corporate-friendly build script:

```bash
export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
./build-corporate.sh
```

This script:
1. ✅ Uses `--allow-unauthenticated` flags for apt
2. ✅ Configures apt to allow insecure repositories
3. ✅ Uses `--trusted-host` for pip installations
4. ✅ Properly installs your EMC CA certificates
5. ✅ Sets up GitHub Enterprise domain configuration

### What's Different in the Corporate Build

The corporate Dockerfile (`Dockerfile.corporate`) includes:

1. **APT Configuration:**
   ```dockerfile
   RUN echo 'Acquire::Check-Valid-Until "false";' > /etc/apt/apt.conf.d/99no-check-valid-until \
       && echo 'Acquire::AllowInsecureRepositories "true";' > /etc/apt/apt.conf.d/99allow-insecure
   ```

2. **Package Installation:**
   ```dockerfile
   RUN apt-get update -y --allow-unauthenticated --allow-insecure-repositories \
       && apt-get install -y --allow-unauthenticated curl make git build-essential ca-certificates
   ```

3. **Python Package Installation:**
   ```dockerfile
   RUN python3 -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org poetry==1.8.2
   ```

### Manual Steps (If Script Fails)

1. **Copy the corporate Dockerfile:**
   ```bash
   cp containers/app/Dockerfile.corporate containers/app/Dockerfile
   ```

2. **Build with Docker Compose:**
   ```bash
   export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
   docker-compose build --no-cache
   ```

3. **Run the application:**
   ```bash
   docker-compose up
   ```

## Verification

After successful build, verify the setup:

```bash
# Test the configuration
python test_github_enterprise.py

# Check that GitHub API calls go to your enterprise instance
docker run --rm -e GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com openhands:latest \
  python -c "from openhands.integrations.github.github_service import GitHubService; print(GitHubService().BASE_URL)"
```

Expected output: `https://eos2git.cec.lab.emc.com/api/v3`

## Troubleshooting

If you still encounter issues:

1. **Check certificate format:**
   ```bash
   openssl x509 -in EMC_CA_ROOT.crt -text -noout
   ```

2. **Verify Docker resources:**
   ```bash
   docker system df
   docker system prune -a  # Clean up if needed
   ```

3. **Test network connectivity:**
   ```bash
   curl -I https://eos2git.cec.lab.emc.com
   ```

## Files Created for Corporate Environment

- `build-corporate.sh` - Corporate-friendly build script
- `containers/app/Dockerfile.corporate` - Corporate Dockerfile
- `DOCKER_TROUBLESHOOTING.md` - Comprehensive troubleshooting guide
- This guide (`CORPORATE_SETUP.md`)

## Next Steps

Once the build succeeds:
1. All GitHub API calls will automatically use your enterprise instance
2. SSL certificates will be properly configured
3. You can use OpenHands normally with your GitHub Enterprise repositories

The system is now configured to use `https://eos2git.cec.lab.emc.com/api/v3/` instead of `https://api.github.com/` for all GitHub operations.
