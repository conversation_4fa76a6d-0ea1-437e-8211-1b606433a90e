#!/bin/bash

# Detailed diagnosis for repository clone failure
echo "🔍 Detailed Diagnosis for Repository Clone Failure"
echo "=================================================="

GITHUB_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
REPO_NAME="wux13/POC6"

echo "📋 Configuration:"
echo "  GitHub Domain: $GITHUB_DOMAIN"
echo "  Repository: $REPO_NAME"
echo "  Expected URL: https://$GITHUB_DOMAIN/$REPO_NAME.git"

# Check if containers are running
echo ""
echo "🐳 Container Status:"
docker-compose ps

# Get container IDs
APP_CONTAINER=$(docker ps -q --filter "name=openhands-app")
RUNTIME_CONTAINER=$(docker ps -q --filter "name=openhands-runtime")

echo "  App Container: ${APP_CONTAINER:-'NOT FOUND'}"
echo "  Runtime Container: ${RUNTIME_CONTAINER:-'NOT FOUND'}"

# Check environment variables in containers
echo ""
echo "📋 Environment Variables in Containers:"
if [ ! -z "$APP_CONTAINER" ]; then
    echo "App Container:"
    docker exec $APP_CONTAINER env | grep -E "(GITHUB|GIT)" | sort || echo "  No GitHub-related env vars found"
else
    echo "❌ App container not running"
fi

if [ ! -z "$RUNTIME_CONTAINER" ]; then
    echo "Runtime Container:"
    docker exec $RUNTIME_CONTAINER env | grep -E "(GITHUB|GIT)" | sort || echo "  No GitHub-related env vars found"
else
    echo "❌ Runtime container not running"
fi

# Check detailed logs for clone attempts
echo ""
echo "📝 Detailed Clone Logs:"
echo "App Container Logs (last 50 lines, filtered for clone/git/error):"
if [ ! -z "$APP_CONTAINER" ]; then
    docker logs $APP_CONTAINER 2>&1 | grep -i -E "(clone|git|error|fail|exception)" | tail -20
else
    echo "❌ No app container to check"
fi

echo ""
echo "Runtime Container Logs (last 50 lines, filtered for workspace/clone/error):"
if [ ! -z "$RUNTIME_CONTAINER" ]; then
    docker logs $RUNTIME_CONTAINER 2>&1 | grep -i -E "(workspace|clone|error|fail|exception)" | tail -20
else
    echo "❌ No runtime container to check"
fi

# Test network connectivity from containers
echo ""
echo "🌐 Network Connectivity Tests:"

if [ ! -z "$APP_CONTAINER" ]; then
    echo "From App Container:"
    
    # Test basic connectivity
    echo "  Testing ping to $GITHUB_DOMAIN:"
    docker exec $APP_CONTAINER ping -c 2 $GITHUB_DOMAIN 2>/dev/null && echo "    ✅ Ping successful" || echo "    ❌ Ping failed"
    
    # Test HTTPS connectivity
    echo "  Testing HTTPS connection:"
    docker exec $APP_CONTAINER curl -I -m 10 https://$GITHUB_DOMAIN 2>/dev/null | head -1 && echo "    ✅ HTTPS connection successful" || echo "    ❌ HTTPS connection failed"
    
    # Test API endpoint
    echo "  Testing API endpoint:"
    docker exec $APP_CONTAINER curl -I -m 10 https://$GITHUB_DOMAIN/api/v3 2>/dev/null | head -1 && echo "    ✅ API endpoint accessible" || echo "    ❌ API endpoint not accessible"
    
    # Test repository URL
    echo "  Testing repository URL:"
    docker exec $APP_CONTAINER curl -I -m 10 https://$GITHUB_DOMAIN/$REPO_NAME 2>/dev/null | head -1 && echo "    ✅ Repository URL accessible" || echo "    ❌ Repository URL not accessible"
    
    # Test with insecure flag
    echo "  Testing with insecure SSL:"
    docker exec $APP_CONTAINER curl -k -I -m 10 https://$GITHUB_DOMAIN 2>/dev/null | head -1 && echo "    ✅ Works with -k flag (SSL issue)" || echo "    ❌ Fails even with -k flag"
fi

# Check SSL certificates in container
echo ""
echo "🔒 SSL Certificate Status in Container:"
if [ ! -z "$APP_CONTAINER" ]; then
    echo "Checking EMC CA certificate installation:"
    docker exec $APP_CONTAINER ls -la /usr/local/share/ca-certificates/ | grep EMC && echo "  ✅ EMC certificate found" || echo "  ❌ EMC certificate not found"
    
    echo "Checking certificate bundle:"
    docker exec $APP_CONTAINER ls -la /etc/ssl/certs/ca-certificates.crt && echo "  ✅ CA bundle exists" || echo "  ❌ CA bundle missing"
    
    echo "Testing SSL connection to GitHub Enterprise:"
    docker exec $APP_CONTAINER openssl s_client -connect $GITHUB_DOMAIN:443 -servername $GITHUB_DOMAIN </dev/null 2>/dev/null | grep -E "(Verify return code|subject|issuer)" || echo "  ❌ SSL connection test failed"
fi

# Test git clone manually in container
echo ""
echo "🧪 Manual Git Clone Test:"
if [ ! -z "$APP_CONTAINER" ]; then
    TEST_DIR="/tmp/manual_clone_test"
    CLONE_URL="https://$GITHUB_DOMAIN/$REPO_NAME.git"
    
    echo "Testing git clone: $CLONE_URL"
    
    # Test 1: Normal clone
    echo "  1. Normal git clone:"
    if docker exec $APP_CONTAINER git clone $CLONE_URL $TEST_DIR 2>&1 | head -5; then
        echo "    ✅ Clone successful"
        docker exec $APP_CONTAINER rm -rf $TEST_DIR
    else
        echo "    ❌ Clone failed"
        
        # Test 2: With SSL verification disabled
        echo "  2. Git clone with SSL verification disabled:"
        if docker exec $APP_CONTAINER sh -c "GIT_SSL_NO_VERIFY=true git clone $CLONE_URL ${TEST_DIR}_nossl" 2>&1 | head -5; then
            echo "    ✅ Clone successful with SSL verification disabled"
            docker exec $APP_CONTAINER rm -rf ${TEST_DIR}_nossl
            echo "    ⚠️  This indicates an SSL certificate issue"
        else
            echo "    ❌ Clone failed even with SSL verification disabled"
        fi
        
        # Test 3: Check git configuration
        echo "  3. Git configuration in container:"
        docker exec $APP_CONTAINER git config --list | grep -E "(http|ssl|cert)" || echo "    No relevant git config found"
    fi
fi

# Check workspace directory structure
echo ""
echo "📁 Workspace Directory Analysis:"
if [ ! -z "$RUNTIME_CONTAINER" ]; then
    echo "Runtime container workspace structure:"
    docker exec $RUNTIME_CONTAINER ls -la /workspace/ 2>/dev/null || echo "  ❌ /workspace directory not accessible"
    docker exec $RUNTIME_CONTAINER df -h /workspace/ 2>/dev/null || echo "  ❌ Cannot check /workspace disk usage"
fi

# Check local workspace
echo ""
echo "Local workspace structure:"
ls -la ./workspace/ 2>/dev/null || echo "  ❌ Local ./workspace directory not found"

echo ""
echo "🔧 Next Steps Based on Results:"
echo "1. If ping fails: Network connectivity issue"
echo "2. If HTTPS fails but ping works: Firewall/proxy issue"
echo "3. If HTTPS works with -k flag: SSL certificate issue"
echo "4. If repository URL returns 404: Repository doesn't exist or no access"
echo "5. If git clone fails with auth error: Token issue"
echo "6. If git clone works with SSL disabled: Certificate problem"
echo ""
echo "Run this script and share the output for specific troubleshooting guidance."
