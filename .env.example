# OpenHands Environment Configuration Example
# Copy this file to .env and modify the values as needed

# GitHub Enterprise Configuration
# Set this to your GitHub Enterprise domain to redirect all GitHub API calls
# from api.github.com to your enterprise instance
# Example: GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com

# Other common environment variables
# LLM_MODEL=gpt-4o
# LLM_API_KEY=your_api_key_here
# LLM_BASE_URL=https://api.openai.com/v1

# Workspace configuration
# WORKSPACE_BASE=./workspace

# Sandbox configuration
# SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik
