#!/bin/bash

# Check SSL certificates for GitHub Enterprise
echo "🔒 Checking SSL Certificates for GitHub Enterprise"
echo "=================================================="

GITHUB_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}

echo "🌐 Testing SSL connection to: $GITHUB_DOMAIN"

# Test SSL connection
echo ""
echo "🔍 SSL Certificate Information:"
openssl s_client -connect $GITHUB_DOMAIN:443 -servername $GITHUB_DOMAIN </dev/null 2>/dev/null | openssl x509 -noout -text | grep -E "(Subject:|Issuer:|Not Before:|Not After:)" || echo "❌ Could not retrieve SSL certificate information"

# Test with curl
echo ""
echo "🌐 Testing HTTPS connectivity:"
if curl -I https://$GITHUB_DOMAIN >/dev/null 2>&1; then
    echo "✅ HTTPS connection successful"
else
    echo "❌ HTTPS connection failed"
    
    # Test with insecure flag
    echo "🔓 Testing with insecure flag:"
    if curl -k -I https://$GITHUB_DOMAIN >/dev/null 2>&1; then
        echo "⚠️  Connection works with -k flag (certificate issue)"
        echo "   This suggests a certificate validation problem"
    else
        echo "❌ Connection fails even with -k flag (network issue)"
    fi
fi

# Check if EMC_CA_ROOT.crt exists
echo ""
echo "📜 Checking EMC CA Certificate:"
if [ -f "EMC_CA_ROOT.crt" ]; then
    echo "✅ EMC_CA_ROOT.crt found"
    
    # Verify certificate format
    if openssl x509 -in EMC_CA_ROOT.crt -text -noout >/dev/null 2>&1; then
        echo "✅ Certificate format is valid"
        
        # Show certificate details
        echo "📋 Certificate details:"
        openssl x509 -in EMC_CA_ROOT.crt -noout -subject -issuer -dates
    else
        echo "❌ Certificate format is invalid"
    fi
else
    echo "❌ EMC_CA_ROOT.crt not found"
    echo "   You may need to obtain the CA certificate for your GitHub Enterprise"
fi

# Test git clone with different SSL settings
echo ""
echo "🧪 Testing git clone with different SSL settings:"

TEST_REPO="https://$GITHUB_DOMAIN/wux13/POC6.git"
TEST_DIR="/tmp/ssl_test_$$"

echo "Testing: $TEST_REPO"

# Test 1: Normal clone
echo "1. Normal git clone:"
if git clone $TEST_REPO $TEST_DIR >/dev/null 2>&1; then
    echo "   ✅ Success"
    rm -rf $TEST_DIR
else
    echo "   ❌ Failed"
    
    # Test 2: With SSL verification disabled
    echo "2. Git clone with SSL verification disabled:"
    if GIT_SSL_NO_VERIFY=true git clone $TEST_REPO ${TEST_DIR}_nossl >/dev/null 2>&1; then
        echo "   ✅ Success (SSL verification disabled)"
        rm -rf ${TEST_DIR}_nossl
        echo "   ⚠️  This indicates an SSL certificate issue"
    else
        echo "   ❌ Failed even with SSL verification disabled"
    fi
fi

echo ""
echo "🔧 Recommendations:"
echo "1. If SSL verification disabled works:"
echo "   - Add your CA certificate to the Docker image"
echo "   - Use the corporate build script: ./build-corporate.sh"
echo ""
echo "2. If connection fails completely:"
echo "   - Check network connectivity"
echo "   - Verify VPN connection"
echo "   - Check firewall settings"
echo ""
echo "3. For Docker containers:"
echo "   - Ensure EMC_CA_ROOT.crt is properly installed"
echo "   - Rebuild with: ./build-corporate.sh"
