#!/bin/bash

# OpenHands GitHub Enterprise Startup Script
echo "🚀 Starting OpenHands with GitHub Enterprise Support"
echo "===================================================="

# Load GitHub Enterprise configuration
if [ -f "github-enterprise.env" ]; then
    echo "📋 Loading GitHub Enterprise configuration..."
    source github-enterprise.env
    echo "✅ Configuration loaded"
else
    echo "⚠️  github-enterprise.env not found, using defaults"
    export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
fi

# Set unique container name
export DATE=$(date +%Y%m%d_%H%M%S)

echo ""
echo "📋 Configuration Summary:"
echo "  GitHub Domain: $GITHUB_BASE_DOMAIN"
echo "  Container Suffix: $DATE"
echo "  Workspace: ${WORKSPACE_BASE:-$PWD/workspace}"

# Verify prerequisites
echo ""
echo "🔍 Verifying Prerequisites..."

# Check Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi
echo "✅ Docker is available"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed or not in PATH"
    exit 1
fi
echo "✅ Docker Compose is available"

# Test GitHub Enterprise connectivity
echo ""
echo "🌐 Testing GitHub Enterprise connectivity..."
if curl -I -m 10 https://$GITHUB_BASE_DOMAIN >/dev/null 2>&1; then
    echo "✅ Can connect to $GITHUB_BASE_DOMAIN"
else
    echo "⚠️  Cannot connect to $GITHUB_BASE_DOMAIN"
    echo "   This might be due to:"
    echo "   - Network connectivity issues"
    echo "   - VPN not connected"
    echo "   - Firewall blocking access"
    echo "   - SSL certificate issues"
    echo ""
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Exiting..."
        exit 1
    fi
fi

# Test API endpoint
echo "🔌 Testing GitHub Enterprise API..."
if curl -I -m 10 https://$GITHUB_BASE_DOMAIN/api/v3 >/dev/null 2>&1; then
    echo "✅ GitHub Enterprise API is accessible"
else
    echo "⚠️  GitHub Enterprise API is not accessible"
    echo "   API endpoint: https://$GITHUB_BASE_DOMAIN/api/v3"
fi

# Prepare workspace
echo ""
echo "📁 Preparing workspace..."
WORKSPACE_DIR=${WORKSPACE_BASE:-$PWD/workspace}
if [ ! -d "$WORKSPACE_DIR" ]; then
    echo "📂 Creating workspace directory: $WORKSPACE_DIR"
    mkdir -p "$WORKSPACE_DIR"
fi
chmod 755 "$WORKSPACE_DIR"
echo "✅ Workspace ready: $WORKSPACE_DIR"

# Stop existing containers
echo ""
echo "🛑 Stopping existing containers..."
docker-compose down

# Clean up orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker container prune -f

# Check if we need to build the image
echo ""
echo "🏗️  Checking Docker image..."
if docker images | grep -q "openhands.*latest"; then
    echo "✅ OpenHands image found"
else
    echo "📦 Building OpenHands image..."
    docker-compose build
fi

# Start containers
echo ""
echo "🚀 Starting OpenHands containers..."
echo "Environment variables:"
echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN"
echo "  DATE=$DATE"
echo "  WORKSPACE_BASE=$WORKSPACE_DIR"

# Export all necessary environment variables
export GITHUB_BASE_DOMAIN
export DATE
export WORKSPACE_BASE="$WORKSPACE_DIR"

# Start with docker-compose
docker-compose up -d

# Wait for containers to start
echo ""
echo "⏳ Waiting for containers to start..."
sleep 15

# Check container status
echo ""
echo "📊 Container Status:"
docker-compose ps

# Check if containers are healthy
APP_CONTAINER=$(docker ps -q --filter "name=openhands-app")
if [ -z "$APP_CONTAINER" ]; then
    echo "❌ App container is not running"
    echo "📝 Recent logs:"
    docker-compose logs --tail=20
    exit 1
fi

echo "✅ App container is running: $APP_CONTAINER"

# Wait a bit more for the service to be ready
echo ""
echo "⏳ Waiting for OpenHands to be ready..."
sleep 10

# Test if the service is responding
echo "🔍 Testing OpenHands service..."
if curl -I -m 10 http://localhost:3000 >/dev/null 2>&1; then
    echo "✅ OpenHands is responding on http://localhost:3000"
else
    echo "⚠️  OpenHands is not responding yet"
    echo "   This is normal and it might take a few more moments"
fi

echo ""
echo "🎉 OpenHands GitHub Enterprise Setup Complete!"
echo "=============================================="
echo ""
echo "🌐 Access OpenHands at: http://localhost:3000"
echo ""
echo "📝 To monitor logs:"
echo "   docker-compose logs -f"
echo ""
echo "🐛 To debug issues:"
echo "   ./diagnose-clone-failure.sh"
echo ""
echo "🛑 To stop:"
echo "   docker-compose down"
echo ""
echo "📋 Configuration used:"
echo "   GitHub Domain: $GITHUB_BASE_DOMAIN"
echo "   Workspace: $WORKSPACE_DIR"
echo "   Container: openhands-app-$DATE"
