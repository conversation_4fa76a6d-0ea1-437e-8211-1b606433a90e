#!/bin/bash

# Auto-fix build script that tries multiple approaches for corporate environments
# This script will automatically try different Dockerfiles until one works

set -e  # Exit on any error

echo "🔧 Auto-fix Docker build for corporate environments"
echo "This script will try multiple approaches until one succeeds"
echo "=================================================="

# Check if EMC_CA_ROOT.crt exists
if [ ! -f "EMC_CA_ROOT.crt" ]; then
    echo "❌ ERROR: EMC_CA_ROOT.crt file not found!"
    echo "Please ensure the certificate file is in the current directory."
    exit 1
fi

echo "✅ Found EMC_CA_ROOT.crt certificate file"

# Set environment variables for the build
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "✅ Using GitHub Enterprise domain: $GITHUB_BASE_DOMAIN"

# Set additional environment variables for corporate environments
export DOCKER_BUILDKIT=0  # Disable BuildKit for better compatibility
export COMPOSE_DOCKER_CLI_BUILD=0

# Backup original Dockerfile
if [ -f "containers/app/Dockerfile" ]; then
    cp containers/app/Dockerfile containers/app/Dockerfile.original
    echo "✅ Backed up original Dockerfile"
fi

# Function to try a build approach
try_build() {
    local approach_name="$1"
    local dockerfile_name="$2"
    
    echo ""
    echo "🚀 Trying approach: $approach_name"
    echo "Using Dockerfile: $dockerfile_name"
    echo "----------------------------------------"
    
    if [ -f "$dockerfile_name" ]; then
        cp "$dockerfile_name" containers/app/Dockerfile
        
        if docker-compose build --no-cache; then
            echo "✅ SUCCESS: $approach_name worked!"
            return 0
        else
            echo "❌ FAILED: $approach_name did not work"
            return 1
        fi
    else
        echo "❌ SKIPPED: $dockerfile_name not found"
        return 1
    fi
}

# Try different approaches in order of preference
echo "Starting build attempts..."

# Approach 1: Current updated Dockerfile (with corporate fixes)
if try_build "Updated Dockerfile with corporate fixes" "containers/app/Dockerfile.original"; then
    success_approach="Updated Dockerfile with corporate fixes"
# Approach 2: Corporate-friendly Dockerfile
elif try_build "Corporate-friendly Dockerfile" "containers/app/Dockerfile.corporate"; then
    success_approach="Corporate-friendly Dockerfile"
# Approach 3: Complete GPG bypass
elif try_build "Complete GPG bypass" "containers/app/Dockerfile.bypass-gpg"; then
    success_approach="Complete GPG bypass"
else
    echo ""
    echo "❌ ALL BUILD APPROACHES FAILED!"
    echo "=================================================="
    echo "None of the following approaches worked:"
    echo "1. Updated Dockerfile with corporate fixes"
    echo "2. Corporate-friendly Dockerfile"
    echo "3. Complete GPG bypass"
    echo ""
    echo "This suggests a more fundamental issue. Please check:"
    echo "1. Docker daemon is running and has sufficient resources"
    echo "2. Network connectivity to package repositories"
    echo "3. Corporate firewall/proxy settings"
    echo "4. Available disk space: $(df -h . | tail -1 | awk '{print $4}') free"
    echo ""
    echo "You may need to:"
    echo "- Configure Docker to use your corporate proxy"
    echo "- Add Docker registry mirrors"
    echo "- Contact your IT department for Docker/network configuration"
    
    # Restore original Dockerfile
    if [ -f "containers/app/Dockerfile.original" ]; then
        mv containers/app/Dockerfile.original containers/app/Dockerfile
    fi
    exit 1
fi

echo ""
echo "🎉 BUILD SUCCESSFUL!"
echo "=================================================="
echo "Successful approach: $success_approach"
echo ""
echo "To run OpenHands with GitHub Enterprise support:"
echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up"
echo ""
echo "Your GitHub API calls will now go to:"
echo "  https://$GITHUB_BASE_DOMAIN/api/v3/"
echo "  instead of https://api.github.com/"
echo ""

# Keep the successful Dockerfile and restore original as backup
if [ -f "containers/app/Dockerfile.original" ]; then
    echo "The working Dockerfile has been kept in place."
    echo "Original Dockerfile backed up as: containers/app/Dockerfile.original"
fi

echo "✅ Setup complete! You can now use OpenHands with your GitHub Enterprise instance."
