#!/bin/bash

# Fix workspace mount issues for GitHub Enterprise
echo "🔧 Fixing Workspace Mount Issues for GitHub Enterprise"
echo "======================================================"

# Set GitHub Enterprise domain
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "✅ Set GITHUB_BASE_DOMAIN to: $GITHUB_BASE_DOMAIN"

# Stop existing containers
echo ""
echo "🛑 Stopping existing containers..."
docker-compose down

# Clean up any orphaned containers
echo "🧹 Cleaning up orphaned containers..."
docker container prune -f

# Clean up volumes if needed (optional - uncomment if you want to start fresh)
# echo "🗑️ Cleaning up volumes..."
# docker volume prune -f

# Check if workspace directory exists locally
echo ""
echo "📁 Checking local workspace..."
if [ -d "./workspace" ]; then
    echo "✅ Local workspace directory exists"
    ls -la ./workspace/
else
    echo "📂 Creating local workspace directory..."
    mkdir -p ./workspace
fi

# Verify GitHub Enterprise connectivity
echo ""
echo "🌐 Testing GitHub Enterprise connectivity..."
if curl -I https://$GITHUB_BASE_DOMAIN >/dev/null 2>&1; then
    echo "✅ Can connect to $GITHUB_BASE_DOMAIN"
else
    echo "❌ Cannot connect to $GITHUB_BASE_DOMAIN"
    echo "   Please check your network connection and VPN settings"
fi

# Test API endpoint
if curl -I https://$GITHUB_BASE_DOMAIN/api/v3 >/dev/null 2>&1; then
    echo "✅ GitHub Enterprise API is accessible"
else
    echo "❌ GitHub Enterprise API is not accessible"
    echo "   Please verify the API endpoint: https://$GITHUB_BASE_DOMAIN/api/v3"
fi

# Check if repository exists
echo ""
echo "🔍 Checking repository accessibility..."
REPO_URL="https://$GITHUB_BASE_DOMAIN/wux13/POC6"
if curl -I $REPO_URL >/dev/null 2>&1; then
    echo "✅ Repository is accessible: $REPO_URL"
else
    echo "❌ Repository is not accessible: $REPO_URL"
    echo "   Please verify:"
    echo "   1. The repository exists on your GitHub Enterprise"
    echo "   2. You have access to the repository"
    echo "   3. Your GitHub token has the necessary permissions"
fi

# Create a test clone to verify credentials
echo ""
echo "🧪 Testing repository clone..."
TEST_DIR="/tmp/test_clone_$$"
if git clone https://$GITHUB_BASE_DOMAIN/wux13/POC6.git $TEST_DIR >/dev/null 2>&1; then
    echo "✅ Repository clone successful"
    rm -rf $TEST_DIR
else
    echo "❌ Repository clone failed"
    echo "   This might be due to:"
    echo "   1. Missing or invalid GitHub token"
    echo "   2. Repository doesn't exist"
    echo "   3. No access permissions"
    echo ""
    echo "   To test manually:"
    echo "   git clone https://$GITHUB_BASE_DOMAIN/wux13/POC6.git"
fi

# Start containers with proper environment
echo ""
echo "🚀 Starting OpenHands with GitHub Enterprise configuration..."
echo "Environment variables:"
echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN"

# Start the containers
GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up -d

# Wait a moment for containers to start
echo ""
echo "⏳ Waiting for containers to start..."
sleep 10

# Check container status
echo ""
echo "📊 Container Status:"
docker-compose ps

# Check logs for any immediate errors
echo ""
echo "📝 Recent logs (last 20 lines):"
docker-compose logs --tail=20

echo ""
echo "✅ Setup complete!"
echo ""
echo "🔍 To monitor the cloning process:"
echo "   docker-compose logs -f"
echo ""
echo "🐛 To debug further:"
echo "   ./debug-github-enterprise.sh"
echo ""
echo "🌐 Access OpenHands at: http://localhost:3000"
