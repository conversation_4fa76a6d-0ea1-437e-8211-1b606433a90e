# Docker Build Troubleshooting Guide

## GitHub Enterprise Configuration for OpenHands

This guide helps you troubleshoot Docker build issues when configuring OpenHands for GitHub Enterprise.

## Quick Start

1. **Set the GitHub Enterprise domain:**
   ```bash
   export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
   ```

2. **Build and run:**
   ```bash
   ./build-docker.sh
   # OR
   docker-compose build && docker-compose up
   ```

## Common Issues and Solutions

### 1. Certificate Update Failure

**Error:** `update-ca-certificates` fails during Docker build

**Solutions:**
- Ensure `EMC_CA_ROOT.crt` is in the correct PEM format
- Check that the certificate file has proper line endings (Unix LF, not Windows CRLF)
- Verify the certificate file is not corrupted

**Test certificate format:**
```bash
openssl x509 -in EMC_CA_ROOT.crt -text -noout
```

### 2. Docker Build Fails with Exit Code 100

**Possible causes:**
- Network connectivity issues during package installation
- Certificate installation problems
- Insufficient disk space

**Solutions:**
```bash
# Clean Docker cache
docker system prune -a

# Build with no cache
docker-compose build --no-cache

# Check disk space
df -h
```

### 3. SSL/TLS Verification Errors

**Error:** SSL certificate verification failures when accessing GitHub Enterprise

**Solutions:**
- Ensure the certificate is properly installed in both build stages
- Verify environment variables are set correctly:
  ```bash
  export REQUESTS_CA_BUNDLE=/etc/ssl/certs/ca-certificates.crt
  export SSL_CERT_FILE=/etc/ssl/certs/ca-certificates.crt
  ```

### 4. GitHub API Access Issues

**Error:** API calls still going to api.github.com instead of your enterprise instance

**Check configuration:**
```bash
# Verify environment variable is set
echo $GITHUB_BASE_DOMAIN

# Check docker-compose.yml has the correct environment variable
grep GITHUB_BASE_DOMAIN docker-compose.yml
```

## Manual Certificate Installation

If automatic installation fails, you can manually install certificates:

```bash
# Copy certificate to the container
docker cp EMC_CA_ROOT.crt container_name:/usr/local/share/ca-certificates/
docker exec container_name update-ca-certificates
```

## Testing the Configuration

1. **Test certificate installation:**
   ```bash
   docker run --rm openhands:latest openssl x509 -in /usr/local/share/ca-certificates/EMC_CA_ROOT.crt -text -noout
   ```

2. **Test GitHub Enterprise connectivity:**
   ```bash
   docker run --rm -e GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com openhands:latest curl -I https://eos2git.cec.lab.emc.com/api/v3
   ```

## Alternative Build Approach

If the standard build continues to fail, try building without certificate installation first:

1. Comment out certificate-related lines in Dockerfile
2. Build the image successfully
3. Add certificates at runtime using volume mounts

## Getting Help

If issues persist:
1. Check Docker logs: `docker-compose logs`
2. Verify certificate format and content
3. Test network connectivity to your GitHub Enterprise instance
4. Ensure Docker has sufficient resources (CPU, memory, disk)

## Environment Variables Reference

- `GITHUB_BASE_DOMAIN`: Your GitHub Enterprise domain (e.g., eos2git.cec.lab.emc.com)
- `REQUESTS_CA_BUNDLE`: Path to CA certificate bundle
- `SSL_CERT_FILE`: Path to SSL certificate file
