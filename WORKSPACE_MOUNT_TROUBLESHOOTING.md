# Workspace Mount Troubleshooting Guide

## 问题描述
- **App容器日志**: `Cloning repo: wux13/POC6`
- **Runtime容器错误**: `Work directory /workspace/POC6 does not exist`

这表明仓库克隆失败或没有正确挂载到runtime容器。

## 🔍 诊断步骤

### 1. 运行诊断脚本
```bash
# 检查整体配置
./debug-github-enterprise.sh

# 检查SSL证书
./check-certificates.sh

# 测试仓库配置
python3 test-repo-clone.py
```

### 2. 手动检查

#### 检查环境变量
```bash
echo $GITHUB_BASE_DOMAIN
# 应该输出: eos2git.cec.lab.emc.com
```

#### 检查容器状态
```bash
docker-compose ps
docker-compose logs app | grep -i clone
docker-compose logs runtime | grep -i workspace
```

#### 检查仓库访问
```bash
# 测试网络连接
curl -I https://eos2git.cec.lab.emc.com

# 测试API端点
curl -I https://eos2git.cec.lab.emc.com/api/v3

# 测试仓库访问
curl -I https://eos2git.cec.lab.emc.com/wux13/POC6
```

## 🔧 解决方案

### 方案1: 重新启动并修复配置
```bash
# 使用修复脚本
./fix-workspace-mount.sh
```

### 方案2: 手动修复步骤

1. **停止容器**
   ```bash
   docker-compose down
   ```

2. **设置环境变量**
   ```bash
   export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
   ```

3. **检查本地workspace**
   ```bash
   mkdir -p ./workspace
   ls -la ./workspace
   ```

4. **重新启动**
   ```bash
   GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com docker-compose up
   ```

### 方案3: SSL证书问题修复

如果是SSL证书问题：

1. **使用企业构建**
   ```bash
   ./build-corporate.sh
   ```

2. **验证证书安装**
   ```bash
   docker run --rm openhands:latest openssl x509 -in /usr/local/share/ca-certificates/EMC_CA_ROOT.crt -text -noout
   ```

## 🐛 常见问题

### 问题1: 仓库不存在
**症状**: `Repository not found` 或 `404 error`
**解决**: 
- 验证仓库路径: `https://eos2git.cec.lab.emc.com/wux13/POC6`
- 检查仓库权限
- 确认GitHub token有效

### 问题2: SSL证书错误
**症状**: `SSL certificate problem` 或 `certificate verify failed`
**解决**:
- 使用 `./build-corporate.sh` 重新构建
- 检查 `EMC_CA_ROOT.crt` 文件
- 验证证书格式: `openssl x509 -in EMC_CA_ROOT.crt -text -noout`

### 问题3: 网络连接问题
**症状**: `Connection refused` 或 `timeout`
**解决**:
- 检查VPN连接
- 验证防火墙设置
- 测试网络连接: `ping eos2git.cec.lab.emc.com`

### 问题4: 权限问题
**症状**: `Permission denied` 或 `Authentication failed`
**解决**:
- 检查GitHub token权限
- 验证token有仓库访问权限
- 确认token未过期

## 📝 验证修复

修复后，验证以下内容：

1. **环境变量正确设置**
   ```bash
   docker exec openhands-app-* env | grep GITHUB_BASE_DOMAIN
   ```

2. **仓库成功克隆**
   ```bash
   docker exec openhands-runtime-* ls -la /workspace/
   ```

3. **API调用使用正确域名**
   ```bash
   docker-compose logs app | grep "eos2git.cec.lab.emc.com"
   ```

## 🚀 快速修复命令

如果你想快速尝试修复：

```bash
# 一键修复脚本
./fix-workspace-mount.sh

# 或者手动执行
export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
docker-compose down
docker-compose up
```

## 📞 获取帮助

如果问题仍然存在：

1. 运行完整诊断: `./debug-github-enterprise.sh`
2. 检查所有日志: `docker-compose logs`
3. 验证网络和证书: `./check-certificates.sh`
4. 提供错误日志和诊断输出以获取进一步帮助
