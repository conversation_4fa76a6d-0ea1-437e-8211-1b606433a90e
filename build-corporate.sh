#!/bin/bash

# Corporate-friendly build script for OpenHands with GitHub Enterprise support
# This script uses the corporate Dockerfile that handles SSL inspection and proxy issues

set -e  # Exit on any error

echo "Building OpenHands Docker image for corporate environment..."

# Check if EMC_CA_ROOT.crt exists
if [ ! -f "EMC_CA_ROOT.crt" ]; then
    echo "ERROR: EMC_CA_ROOT.crt file not found!"
    echo "Please ensure the certificate file is in the current directory."
    exit 1
fi

echo "Found EMC_CA_ROOT.crt certificate file"

# Set environment variables for the build
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "Using GitHub Enterprise domain: $GITHUB_BASE_DOMAIN"

# Backup original Dockerfile and use corporate version
echo "Switching to corporate-friendly Dockerfile..."
if [ -f "containers/app/Dockerfile" ]; then
    cp containers/app/Dockerfile containers/app/Dockerfile.original
fi
cp containers/app/Dockerfile.corporate containers/app/Dockerfile

# Build the Docker image with corporate-friendly settings
echo "Starting Docker build with corporate settings..."
echo "This build uses --allow-unauthenticated flags to handle SSL inspection"

# Set additional environment variables for corporate environments
export DOCKER_BUILDKIT=0  # Disable BuildKit for better compatibility
export COMPOSE_DOCKER_CLI_BUILD=0

docker-compose build --no-cache

if [ $? -eq 0 ]; then
    echo "✅ Corporate Docker build completed successfully!"
    echo ""
    echo "To run OpenHands with GitHub Enterprise support:"
    echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up"
    echo ""
    echo "The build used corporate-friendly settings including:"
    echo "  - --allow-unauthenticated for apt packages"
    echo "  - --trusted-host for pip installations"
    echo "  - Custom certificate installation"
    echo ""
    
    # Restore original Dockerfile
    if [ -f "containers/app/Dockerfile.original" ]; then
        echo "Restoring original Dockerfile..."
        mv containers/app/Dockerfile.original containers/app/Dockerfile
    fi
else
    echo "❌ Corporate Docker build failed!"
    echo ""
    echo "Troubleshooting steps:"
    echo "1. Check your network connectivity"
    echo "2. Verify proxy settings if using a corporate proxy"
    echo "3. Ensure Docker has sufficient resources"
    echo "4. Check the certificate format: openssl x509 -in EMC_CA_ROOT.crt -text -noout"
    
    # Restore original Dockerfile
    if [ -f "containers/app/Dockerfile.original" ]; then
        echo "Restoring original Dockerfile..."
        mv containers/app/Dockerfile.original containers/app/Dockerfile
    fi
    exit 1
fi
