# GitHub Enterprise Setup for OpenHands

## 🚀 Quick Start

### 1. Basic Setup (No SSL Issues)
```bash
# Set your GitHub Enterprise domain
export GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com

# Start OpenHands
./start-github-enterprise.sh
```

### 2. Corporate Setup (With SSL Certificates)
```bash
# 1. Place your corporate CA certificate as EMC_CA_ROOT.crt
# 2. Build with corporate settings
./build-corporate.sh

# 3. Start OpenHands
./start-github-enterprise.sh
```

## 🔧 Configuration Files

### Environment Configuration
Edit `github-enterprise.env`:
```bash
GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com
GITHUB_TOKEN=your_token_here  # Optional for private repos
```

## 🐛 Troubleshooting

### If Repository Clone Fails

1. **Run Diagnostics**
   ```bash
   ./diagnose-clone-failure.sh
   ```

2. **Quick Tests**
   ```bash
   ./quick-clone-test.sh
   ```

3. **Check Logs**
   ```bash
   docker-compose logs -f | grep -i clone
   ```

### Common Issues & Solutions

#### SSL Certificate Problems
```bash
# Symptoms: SSL verification failed, certificate errors
./build-corporate.sh
```

#### Repository Not Found
```bash
# Verify repository exists and you have access
curl -I https://eos2git.cec.lab.emc.com/wux13/POC6
```

#### Authentication Issues
```bash
# Set GitHub token and restart
export GITHUB_TOKEN=your_token_here
./start-github-enterprise.sh
```

#### Network Connectivity
```bash
# Test basic connectivity
ping eos2git.cec.lab.emc.com
curl -I https://eos2git.cec.lab.emc.com
```

## 📋 Available Scripts

| Script | Purpose |
|--------|---------|
| `start-github-enterprise.sh` | Main startup script with full checks |
| `build-corporate.sh` | Build with SSL certificates |
| `diagnose-clone-failure.sh` | Detailed diagnostics |
| `quick-clone-test.sh` | Quick connectivity test |
| `fix-workspace-mount.sh` | Legacy fix script |

## 🔍 Monitoring

### View Logs
```bash
# All logs
docker-compose logs -f

# Clone-specific logs
docker-compose logs -f | grep -i clone

# Error logs only
docker-compose logs -f | grep -i error
```

### Check Container Status
```bash
docker-compose ps
```

### Access OpenHands
- URL: http://localhost:3000
- The interface will show repository cloning progress

## ⚙️ Advanced Configuration

### Custom Workspace Location
```bash
export WORKSPACE_BASE=/path/to/your/workspace
./start-github-enterprise.sh
```

### Debug Mode
```bash
# Enable verbose logging
export DEBUG=1
./start-github-enterprise.sh
```

### SSL Troubleshooting
```bash
# Test SSL connection
openssl s_client -connect eos2git.cec.lab.emc.com:443

# Disable SSL verification (temporary)
export GIT_SSL_NO_VERIFY=true
```

## 🆘 Getting Help

1. **Run full diagnostics**: `./diagnose-clone-failure.sh`
2. **Check the troubleshooting guide**: `WORKSPACE_MOUNT_TROUBLESHOOTING.md`
3. **View container logs**: `docker-compose logs`
4. **Test connectivity**: `./quick-clone-test.sh`

## 📝 Notes

- The system automatically detects your GitHub Enterprise domain
- SSL certificates are handled automatically if using `build-corporate.sh`
- Repository cloning happens in the background after starting OpenHands
- Check logs for detailed error messages if cloning fails
