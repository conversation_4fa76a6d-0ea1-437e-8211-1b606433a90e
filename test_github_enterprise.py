#!/usr/bin/env python3
"""
Test script to verify GitHub Enterprise configuration in OpenHands
"""

import os
import sys
import requests
from urllib.parse import urlparse

def test_github_enterprise_config():
    """Test that GitHub Enterprise configuration is working correctly"""
    
    # Test 1: Check environment variable
    github_domain = os.environ.get('GITHUB_BASE_DOMAIN', 'github.com')
    print(f"✓ GitHub Base Domain: {github_domain}")
    
    # Test 2: Test configuration loading
    try:
        from openhands.core.config.utils import load_app_config
        app_config = load_app_config()
        config_domain = getattr(app_config, 'github_base_domain', 'github.com')
        print(f"✓ Config GitHub Domain: {config_domain}")
    except Exception as e:
        print(f"⚠ Could not load app config: {e}")
        config_domain = github_domain
    
    # Test 3: Test GitHubService initialization
    try:
        from openhands.integrations.github.github_service import GitHubService
        from pydantic import SecretStr
        
        service = GitHubService(token=SecretStr('dummy_token'))
        print(f"✓ GitHubService BASE_URL: {service.BASE_URL}")
        
        # Check if it's using the enterprise domain
        if github_domain != 'github.com' and github_domain in service.BASE_URL:
            print("✅ GitHubService is correctly configured for GitHub Enterprise")
        elif github_domain == 'github.com' and 'api.github.com' in service.BASE_URL:
            print("✅ GitHubService is correctly configured for public GitHub")
        else:
            print("❌ GitHubService configuration mismatch")
            
    except Exception as e:
        print(f"❌ Error testing GitHubService: {e}")
    
    # Test 4: Test GitHub Issue Handler
    try:
        from openhands.resolver.interfaces.github import GithubIssueHandler
        
        handler = GithubIssueHandler('test', 'test', 'dummy_token')
        print(f"✓ GithubIssueHandler base_domain: {handler.base_domain}")
        print(f"✓ GithubIssueHandler base_url: {handler.base_url}")
        
        if github_domain != 'github.com' and github_domain in handler.base_url:
            print("✅ GithubIssueHandler is correctly configured for GitHub Enterprise")
        elif github_domain == 'github.com' and 'api.github.com' in handler.base_url:
            print("✅ GithubIssueHandler is correctly configured for public GitHub")
        else:
            print("❌ GithubIssueHandler configuration mismatch")
            
    except Exception as e:
        print(f"❌ Error testing GithubIssueHandler: {e}")
    
    # Test 5: Test certificate installation (if not using public GitHub)
    if github_domain != 'github.com':
        try:
            # Test if we can resolve the GitHub Enterprise domain
            test_url = f"https://{github_domain}"
            print(f"Testing connectivity to {test_url}...")
            
            response = requests.head(test_url, timeout=10, verify=True)
            print(f"✅ Successfully connected to {github_domain} (Status: {response.status_code})")
            
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL Error connecting to {github_domain}: {e}")
            print("   This might indicate certificate issues")
        except requests.exceptions.ConnectionError as e:
            print(f"⚠ Connection Error to {github_domain}: {e}")
            print("   This might be expected if the domain is not publicly accessible")
        except Exception as e:
            print(f"⚠ Error testing connectivity to {github_domain}: {e}")
    
    print("\n" + "="*60)
    print("Configuration Summary:")
    print(f"GitHub Domain: {github_domain}")
    print(f"Expected API Base: https://{github_domain}/api/v3" if github_domain != 'github.com' else "https://api.github.com")
    print("="*60)

if __name__ == "__main__":
    print("Testing OpenHands GitHub Enterprise Configuration")
    print("="*60)
    
    # Add the current directory to Python path to import OpenHands modules
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    test_github_enterprise_config()
