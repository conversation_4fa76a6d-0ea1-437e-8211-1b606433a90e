#!/bin/bash

# Ultra-aggressive build script for corporate environments with strict SSL inspection
# This completely bypasses GPG verification for package repositories

set -e  # Exit on any error

echo "Building OpenHands with complete GPG bypass for corporate environments..."

# Check if EMC_CA_ROOT.crt exists
if [ ! -f "EMC_CA_ROOT.crt" ]; then
    echo "ERROR: EMC_CA_ROOT.crt file not found!"
    echo "Please ensure the certificate file is in the current directory."
    exit 1
fi

echo "Found EMC_CA_ROOT.crt certificate file"

# Set environment variables for the build
export GITHUB_BASE_DOMAIN=${GITHUB_BASE_DOMAIN:-eos2git.cec.lab.emc.com}
echo "Using GitHub Enterprise domain: $GITHUB_BASE_DOMAIN"

# Backup original Dockerfile and use GPG bypass version
echo "Switching to GPG bypass Dockerfile..."
if [ -f "containers/app/Dockerfile" ]; then
    cp containers/app/Dockerfile containers/app/Dockerfile.backup
fi
cp containers/app/Dockerfile.bypass-gpg containers/app/Dockerfile

# Set additional environment variables for corporate environments
export DOCKER_BUILDKIT=0  # Disable BuildKit for better compatibility
export COMPOSE_DOCKER_CLI_BUILD=0

echo "Starting Docker build with complete GPG bypass..."
echo "This build completely disables package signature verification"

docker-compose build --no-cache

if [ $? -eq 0 ]; then
    echo "✅ GPG bypass Docker build completed successfully!"
    echo ""
    echo "To run OpenHands with GitHub Enterprise support:"
    echo "  GITHUB_BASE_DOMAIN=$GITHUB_BASE_DOMAIN docker-compose up"
    echo ""
    echo "The build used ultra-aggressive settings including:"
    echo "  - Complete GPG verification bypass"
    echo "  - [trusted=yes] for all repositories"
    echo "  - --trusted-host for all pip installations"
    echo "  - Custom certificate installation"
    echo ""
    
    # Restore original Dockerfile
    if [ -f "containers/app/Dockerfile.backup" ]; then
        echo "Restoring original Dockerfile..."
        mv containers/app/Dockerfile.backup containers/app/Dockerfile
    fi
else
    echo "❌ GPG bypass Docker build failed!"
    echo ""
    echo "This was the most aggressive approach. If this fails, the issue might be:"
    echo "1. Network connectivity problems"
    echo "2. Docker daemon issues"
    echo "3. Insufficient system resources"
    echo "4. Corporate firewall blocking Docker registry access"
    
    # Restore original Dockerfile
    if [ -f "containers/app/Dockerfile.backup" ]; then
        echo "Restoring original Dockerfile..."
        mv containers/app/Dockerfile.backup containers/app/Dockerfile
    fi
    exit 1
fi
