# Résolveur GitHub Cloud

Le Résolveur GitHub automatise les corrections de code et fournit une assistance intelligente pour vos dépôts.

## Configuration

Le Résolveur GitHub Cloud est disponible automatiquement lorsque vous
[accordez l'accès au dépôt OpenHands Cloud](./openhands-cloud#adding-repository-access).

## Utilisation

Après avoir accordé l'accès au dépôt OpenHands Cloud, vous pouvez utiliser le Résolveur GitHub Cloud sur les problèmes et les pull requests
du dépôt.

### Problèmes (Issues)

Sur votre dépôt, étiquetez un problème avec `openhands`. OpenHands va :
1. Commenter le problème pour vous informer qu'il y travaille.
    - Vous pouvez cliquer sur le lien pour suivre la progression sur OpenHands Cloud.
2. Ouvrir une pull request s'il détermine que le problème a été résolu avec succès.
3. Commenter le problème avec un résumé des tâches effectuées et un lien vers la pull request.


### Pull Requests

Pour qu'OpenHands travaille sur des pull requests, utilisez `@openhands` dans les commentaires de premier niveau ou en ligne pour :
     - Poser des questions
     - Demander des mises à jour
     - Obtenir des explications de code

OpenHands va :
1. Commenter la PR pour vous informer qu'il y travaille.
2. Effectuer la tâche.
