# OpenHands Cloud

OpenHands Cloud est la version hébergée dans le cloud d'OpenHands par All Hands AI.

## Accéder à OpenHands Cloud

OpenHands Cloud est accessible à l'adresse https://app.all-hands.dev/.

Vous pouvez également interagir avec OpenHands Cloud par programmation en utilisant l'[API](./cloud-api).

## Premiers pas

Après avoir visité OpenHands Cloud, il vous sera demandé de vous connecter avec votre compte GitHub ou GitLab :

1. Après avoir lu et accepté les conditions d'utilisation, cliquez sur `Log in with GitHub` ou `Log in with GitLab`.
2. Examinez les autorisations demandées par OpenHands, puis cliquez sur `Authorize OpenHands AI`.
   - OpenHands nécessitera certaines autorisations de votre compte GitHub ou GitLab. Pour en savoir plus sur ces autorisations :
     - GitHub : Vous pouvez cliquer sur le lien `Learn more` sur la page d'autorisation GitHub.
     - GitLab : Vous pouvez développer chaque demande d'autorisation sur la page d'autorisation GitLab.

## Accès aux dépôts

### GitHub

#### Ajouter l'accès aux dépôts

Vous pouvez accorder à OpenHands un accès à des dépôts spécifiques :
1. Cliquez sur `Add GitHub repos` sur la page d'accueil.
2. Sélectionnez l'organisation, puis choisissez les dépôts spécifiques auxquels vous souhaitez donner accès à OpenHands.
   <details>
     <summary>Détails des autorisations pour l'accès aux dépôts</summary>

     Openhands demande des jetons à courte durée de vie (expiration de 8 heures) avec ces autorisations :
     - Actions : Lecture et écriture
     - Administration : Lecture seule
     - Statuts de commit : Lecture et écriture
     - Contenus : Lecture et écriture
     - Issues : Lecture et écriture
     - Métadonnées : Lecture seule
     - Pull requests : Lecture et écriture
     - Webhooks : Lecture et écriture
     - Workflows : Lecture et écriture

     L'accès au dépôt pour un utilisateur est accordé en fonction de :
     - L'autorisation accordée pour le dépôt.
     - Les autorisations GitHub de l'utilisateur (propriétaire/collaborateur).
   </details>

3. Cliquez sur `Install & Authorize`.

#### Modifier l'accès aux dépôts

Vous pouvez modifier l'accès aux dépôts GitHub à tout moment en :
* Utilisant le même processus `Add GitHub repos`, ou
* Visitant la page Paramètres et en sélectionnant `Configure GitHub Repositories` dans la section `Git Settings`.

### GitLab

Lorsque vous utilisez votre compte GitLab, OpenHands aura automatiquement accès à vos dépôts.

## Persistance des conversations

- Liste des conversations – Affiche uniquement les 10 conversations les plus récentes initiées au cours des 10 derniers jours.
- Espaces de travail – Les espaces de travail de conversation sont conservés pendant 14 jours.
- Environnements d'exécution – Les environnements d'exécution restent actifs ("chauds") pendant 30 minutes. Après cette période, la reprise d'une conversation peut prendre 1 à 2 minutes.
