# クラウド課題リゾルバー

クラウド課題リゾルバーは、GitHubとGitLabのリポジトリに対してコード修正を自動化し、インテリジェントな支援を提供します。

## セットアップ

クラウド課題リゾルバーは、OpenHands Cloudリポジトリアクセスを許可すると自動的に利用可能になります：
- [GitHubリポジトリアクセス](./github-installation#adding-repository-access)
- [GitLabリポジトリアクセス](./gitlab-installation#adding-repository-access)

## 使用方法

OpenHands Cloudリポジトリアクセスを許可した後、リポジトリの課題やプルリクエスト/マージリクエストでクラウド課題リゾルバーを使用できます。

### 課題の操作

リポジトリで、課題に`openhands`というラベルを付けます。OpenHandsは以下を行います：
1. 課題にコメントして、作業中であることを知らせます
   - リンクをクリックすると、OpenHands Cloudで進捗状況を追跡できます
2. 課題が正常に解決されたと判断した場合、プルリクエスト（GitHub）またはマージリクエスト（GitLab）を開きます
3. 実行されたタスクの概要とPR/MRへのリンクを含むコメントを課題に残します

### プルリクエスト/マージリクエストの操作

プルリクエスト（GitHub）またはマージリクエスト（GitLab）でOpenHandsを動作させるには、コメントで`@openhands`を言及して以下を行います：
- 質問する
- 更新をリクエストする
- コードの説明を取得する

OpenHandsは以下を行います：
1. 作業中であることを知らせるコメントをします
2. リクエストされたタスクを実行します
