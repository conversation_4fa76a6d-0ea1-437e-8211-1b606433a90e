# GitHub インストール

このガイドでは、GitHubリポジトリ用にOpenHands Cloudをインストールおよび設定するプロセスについて説明します。

## 前提条件

- GitHubアカウント
- OpenHands Cloudへのアクセス

## インストール手順

1. [OpenHands Cloud](https://app.all-hands.dev)にログインします
2. まだGitHubアカウントを接続していない場合：
   - `GitHubに接続する`をクリックします
   - 利用規約を確認して同意します
   - OpenHands AIアプリケーションを承認します

## リポジトリアクセスの追加

特定のリポジトリへのアクセスをOpenHandsに許可できます：

1. `GitHubプロジェクトを選択`ドロップダウンをクリックし、`リポジトリを追加...`を選択します
2. 組織を選択し、OpenHandsにアクセスを許可する特定のリポジトリを選択します。
   - OpenHandsは以下の権限を持つ短期間のトークン（8時間の有効期限）をリクエストします：
     - アクション：読み取りと書き込み
     - 管理：読み取り専用
     - コミットステータス：読み取りと書き込み
     - コンテンツ：読み取りと書き込み
     - 課題：読み取りと書き込み
     - メタデータ：読み取り専用
     - プルリクエスト：読み取りと書き込み
     - Webhook：読み取りと書き込み
     - ワークフロー：読み取りと書き込み
   - ユーザーのリポジトリアクセスは以下に基づいて付与されます：
     - リポジトリに付与された権限
     - ユーザーのGitHub権限（所有者/コラボレーター）
3. `インストール＆承認`をクリックします

## リポジトリアクセスの変更

リポジトリアクセスはいつでも変更できます：
* 同じ`GitHubプロジェクトを選択 > リポジトリを追加`ワークフローを使用する、または
* 設定ページにアクセスし、`GitHub設定`セクションで`GitHubリポジトリを設定する`を選択します。

## GitHubでのOpenHandsの使用

リポジトリアクセスを許可すると、GitHubリポジトリでOpenHandsを使用できます。

GitHub課題とプルリクエストでOpenHandsを使用する方法の詳細については、[クラウド課題リゾルバー](./cloud-issue-resolver.md)のドキュメントを参照してください。

## 次のステップ

- [クラウドUIにアクセスする](./cloud-ui.md)でウェブインターフェースと対話する
- [クラウド課題リゾルバーを使用する](./cloud-issue-resolver.md)でコード修正を自動化し、支援を受ける
- [クラウドAPIを使用する](./cloud-api.md)でプログラムによりOpenHandsと対話する
