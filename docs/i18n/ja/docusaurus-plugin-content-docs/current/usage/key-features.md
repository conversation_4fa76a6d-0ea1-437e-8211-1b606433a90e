# OpenHands 機能概要

![概要](/img/oh-features.png)

### チャットパネル
- ユーザーとOpenHandsの会話を表示します。
- OpenHandsはこのパネルで自身の行動を説明します。

### 変更点
- OpenHandsによって実行されたファイルの変更を表示します。

### VS Code
- ファイルの閲覧や修正のための組み込みVS Code。
- ファイルのアップロードやダウンロードにも使用できます。

### ターミナル
- OpenHandsとユーザーがターミナルコマンドを実行するためのスペース。

### Jupyter
- OpenHandsによって実行されたすべてのPythonコマンドを表示します。
- OpenHandsを使用してデータ可視化タスクを実行する際に特に便利です。

### アプリ
- OpenHandsがアプリケーションを実行する際にウェブサーバーを表示します。
- ユーザーは実行中のアプリケーションと対話できます。

### ブラウザ
- OpenHandsがウェブサイトを閲覧するために使用します。
- このブラウザは非対話型です。
