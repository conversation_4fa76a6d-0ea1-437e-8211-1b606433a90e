# Visão Geral dos Recursos do OpenHands

![visão geral](/img/oh-features.png)

### Painel de Chat
- Exibe a conversa entre o usuário e o OpenHands.
- O OpenHands explica suas ações neste painel.

### Mudanças
- <PERSON><PERSON> as alterações de arquivos realizadas pelo OpenHands.

### VS Code
- VS Code incorporado para navegar e modificar arquivos.
- Também pode ser usado para fazer upload e download de arquivos.

### Terminal
- Um espaço para o OpenHands e os usuários executarem comandos de terminal.

### Jupyter
- Mostra todos os comandos Python que foram executados pelo OpenHands.
- Particularmente útil ao usar o OpenHands para realizar tarefas de visualização de dados.

### App
- Exibe o servidor web quando o OpenHands executa uma aplicação.
- Os usuários podem interagir com a aplicação em execução.

### Navegador
- Usado pelo OpenHands para navegar em sites.
- O navegador não é interativo.
