{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "node generate-swagger-ui.js && docusaurus start", "build": "node generate-swagger-ui.js && docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "typecheck": "tsc", "generate-swagger-ui": "node generate-swagger-ui.js"}, "// Note": "The OpenAPI spec is stored in docs/static/openapi.json so it's accessible at /openapi.json in the deployed site", "dependencies": {"@docusaurus/core": "^3.7.0", "@docusaurus/plugin-content-pages": "^3.7.0", "@docusaurus/preset-classic": "^3.7.0", "@docusaurus/theme-mermaid": "^3.7.0", "@mdx-js/react": "^3.1.0", "@node-rs/jieba": "^2.0.1", "clsx": "^2.0.0", "docusaurus-lunr-search": "^3.6.0", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-use": "^17.6.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.5.1", "@docusaurus/tsconfig": "^3.7.0", "@docusaurus/types": "^3.5.1", "swagger-cli": "^4.0.4", "swagger-ui-dist": "^5.22.0", "typescript": "~5.8.3"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}, "packageManager": "npm@10.5.0"}