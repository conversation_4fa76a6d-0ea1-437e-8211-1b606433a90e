# GitHub Enterprise Configuration for OpenHands
# This file contains environment variables for GitHub Enterprise setup

# GitHub Enterprise domain (replace with your actual domain)
GITHUB_BASE_DOMAIN=eos2git.cec.lab.emc.com

# Optional: GitHub token for private repositories
# Uncomment and set your token if needed
# GITHUB_TOKEN=your_github_enterprise_token_here

# Optional: Workspace configuration
# WORKSPACE_BASE=./workspace

# Optional: Sandbox configuration
# SANDBOX_RUNTIME_CONTAINER_IMAGE=docker.all-hands.dev/all-hands-ai/runtime:0.39-nikolaik

# Date for unique container names
DATE=$(date +%Y%m%d_%H%M%S)

# Git configuration for SSL (uncomment if you have SSL issues)
# GIT_SSL_NO_VERIFY=true
